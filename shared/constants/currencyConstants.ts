// shared/constants/currencyConstants.ts - Centralized currency constants from API
import { Currency } from '@/shared/query/useCurrenciesQuery';

/**
 * Default currency configuration for the application
 * These values should match the currency API response
 */
export interface DefaultCurrencyConfig {
  id: number;
  code: string;
  name: string;
}

/**
 * Default currency constants - these should be updated based on your API response
 * Currently set to common defaults, but should be replaced with actual API values
 */
export const DEFAULT_CURRENCY: DefaultCurrencyConfig = {
  id: 1, // This should be updated based on your currency API response
  code: 'USD', // This should be updated based on your currency API response  
  name: 'US Dollar' // This should be updated based on your currency API response
};

/**
 * Common currency mappings - extend this based on your API response
 * This provides a fallback mapping when API is not available
 */
export const CURRENCY_MAPPINGS: Record<string, DefaultCurrencyConfig> = {
  USD: { id: 1, code: 'USD', name: 'US Dollar' },
  LKR: { id: 14, code: '<PERSON>K<PERSON>', name: 'Sri Lankan Rupee' },
  EUR: { id: 2, code: 'EUR', name: 'Euro' },
  GBP: { id: 3, code: 'GBP', name: 'British Pound' },
  INR: { id: 4, code: 'INR', name: 'Indian Rupee' },
};

/**
 * Get default currency ID for user creation/update
 * This should be used instead of hardcoded values
 */
export const getDefaultCurrencyId = (): number => {
  return DEFAULT_CURRENCY.id;
};

/**
 * Get default currency code for user creation/update
 * This should be used instead of hardcoded values
 */
export const getDefaultCurrencyCode = (): string => {
  return DEFAULT_CURRENCY.code;
};

/**
 * Find currency by code from the mappings
 * Useful for converting currency codes to IDs
 */
export const getCurrencyByCode = (code: string): DefaultCurrencyConfig | null => {
  return CURRENCY_MAPPINGS[code.toUpperCase()] || null;
};

/**
 * Find currency by ID from the mappings
 * Useful for converting currency IDs to codes
 */
export const getCurrencyById = (id: number): DefaultCurrencyConfig | null => {
  return Object.values(CURRENCY_MAPPINGS).find(currency => currency.id === id) || null;
};

/**
 * Convert API currency response to our internal format
 * Use this to process currency data from the API
 */
export const convertApiCurrencyToInternal = (apiCurrency: Currency): DefaultCurrencyConfig => {
  return {
    id: apiCurrency.id,
    code: apiCurrency.code,
    name: apiCurrency.name
  };
};

/**
 * Update default currency configuration
 * This should be called when currency API data is available
 * Typically called during app initialization or when currencies are fetched
 */
export const updateDefaultCurrency = (currency: DefaultCurrencyConfig): void => {
  // In a real implementation, you might want to store this in localStorage
  // or a global state management solution
  Object.assign(DEFAULT_CURRENCY, currency);
};

/**
 * Get currency configuration from API response
 * Returns the first currency as default, or USD if none found
 */
export const getDefaultCurrencyFromApiResponse = (currencies: Currency[]): DefaultCurrencyConfig => {
  if (currencies && currencies.length > 0) {
    // Look for USD first, then take the first available currency
    const usdCurrency = currencies.find(c => c.code.toUpperCase() === 'USD');
    const defaultCurrency = usdCurrency || currencies[0];
    
    return convertApiCurrencyToInternal(defaultCurrency);
  }
  
  // Fallback to hardcoded USD
  return DEFAULT_CURRENCY;
};

/**
 * Validate if a currency ID exists in the known mappings
 */
export const isValidCurrencyId = (id: number): boolean => {
  return Object.values(CURRENCY_MAPPINGS).some(currency => currency.id === id);
};

/**
 * Validate if a currency code exists in the known mappings
 */
export const isValidCurrencyCode = (code: string): boolean => {
  return code.toUpperCase() in CURRENCY_MAPPINGS;
};
